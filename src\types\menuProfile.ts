interface ProfileMenuItem {
  id?: string;
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  path?: string;
  action?: () => void;
  count?: string | number;
  authenticationRequired: boolean;
  isAdmin: boolean;
  hasToggle?: boolean;
  toggleChecked?: boolean;
  onToggleChange?: (checked: boolean) => void;
  toggleDisabled?: boolean;
}

export type ProfileMenuItems = ProfileMenuItem[];