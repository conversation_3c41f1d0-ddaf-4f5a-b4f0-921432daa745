
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useQueryClient } from "@tanstack/react-query";

export const useLogout = () => {
  const queryClient = useQueryClient();

  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;

      // Clear all React Query cache to ensure no stale user data remains
      queryClient.clear();

    //  toast.success("Logout effettuato con successo");
    } catch (error) {
      console.error('Error during logout:', error);
      toast.error("Errore durante il logout");
    }
  };

  return { logout };
};
