import { motion } from "framer-motion";
import { LogOut, Store, User, RefreshCw, MapPin } from "lucide-react";
import { TermsModal } from "@/components/auth/TermsModal";
import { PrivacyModal } from "@/components/auth/PrivacyModal";
import { useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";

import { Switch } from "@/components/ui/switch";
import { useAppVersion } from "@/hooks/useAppVersion";
import { locationService } from "@/services/locationService";
import RewardsCard from "@/components/profile/RewardsCard";
import { useSubscription } from "@/features/subscription/hook/useSubscription";
import { DemoCoordinatesEditor } from "@/components/profile/DemoCoordinatesEditor";
import { useUserDetails } from "@/hooks/auth/useUserDetails";
import { refreshLocationSettings } from "@/contexts/LocationContext";

import getProfileMenuItems from "./profile_menu";
import { useABTest } from "@/contexts/ABTestContext";
import { getPalette } from "@/styles/colorPalettes";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { getUserDetailsQueryOptions } from "@/queryOptions/getAiAssistantProfilesQueryOptions";
import { useGuestLocationSettings } from "@/stores/useLocationStore";

const Profile = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  const { isBusinessMode, toggleBusinessMode } = useBusinessMode();
  const [mapDemo, setMapDemo] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const { data: appVersion } = useAppVersion();
  const [isTermsOpen, setIsTermsOpen] = useState(false);
  const [isPrivacyOpen, setIsPrivacyOpen] = useState(false);
  const { variants, setColorPalette } = useABTest();
  const { hasActiveSubscription } = useSubscription();
  const { isLocationEnabledForGuests, updateGuestLocationSetting } =
    useGuestLocationSettings();

  // Get menu items with the state setters passed in
  const menuItems = getProfileMenuItems(setIsTermsOpen, setIsPrivacyOpen);

  // TODO Use suspense pattern  https://www.youtube.com/watch?v=mPaCnwpFvZY
  const { data: currentUser, isLoading: isLoadingUser } = useQuery({
    ...getUserDetailsQueryOptions(),
    enabled: true, // Always enabled, but the queryFn will handle auth check
    retry: false, // Don't retry on auth failures
  });
  const { userDetails, updateDemoCoordinates, getDemoCoordinates } =
    useUserDetails(currentUser?.id);

  // Get demo coordinates from the JSON field
  const demoCoords = getDemoCoordinates();

  useEffect(() => {
    const fetchMapDemoSetting = async () => {
      if (!currentUser) return; // Only fetch for authenticated users

      setIsLoading(true);
      try {
        console.log("Fetching user's map demo setting...");
        const { data, error } = await supabase
          .from("user_details")
          .select("map_demo")
          .eq("id", currentUser.id)
          .maybeSingle();

        if (error) {
          console.error(
            "Errore nel recupero dell'impostazione map_demo utente:",
            error
          );
          toast.error("Errore nel recupero dell'impostazione mappa");
          return;
        }

        console.log("Fetched user data:", data);
        if (data) {
          setMapDemo(data.map_demo ?? false); // Default to false if null
        }
      } catch (error) {
        console.error("Errore imprevisto:", error);
        toast.error("Errore nel caricamento dell'impostazione mappa");
      } finally {
        setIsLoading(false);
      }
    };

    fetchMapDemoSetting();
  }, [refreshKey, currentUser]); // Add user as dependency

  const handleMapDemoToggle = async (checked: boolean) => {
    if (!currentUser) return; // Only allow toggle for authenticated users

    setIsLoading(true);
    try {
      console.log("Updating user's map demo setting to:", checked);
      const { error } = await supabase
        .from("user_details")
        .update({ map_demo: checked })
        .eq("id", currentUser.id);

      if (error) {
        toast.error("Errore nell'aggiornamento dell'impostazione");
        console.error(
          "Errore nell'aggiornamento dell'impostazione map_demo utente:",
          error
        );
        return;
      }

      console.log("Setting user demo mode to:", checked);
      // First refresh settings to get latest demo coordinates, then set demo mode
      await refreshLocationSettings();
      locationService.setDemoMode(checked);
      setMapDemo(checked);
      toast.success("Impostazione aggiornata con successo");
    } catch (error) {
      toast.error("Si è verificato un errore");
      console.error("Errore imprevisto:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const refreshSettings = async () => {
    setRefreshKey((prev) => prev + 1);
    toast.info("Aggiornamento impostazioni...");

    // Also refresh location service settings
    try {
      await refreshLocationSettings();
      toast.success("Impostazioni aggiornate");
    } catch (error) {
      console.error("Error refreshing location settings:", error);
      toast.error("Errore nell'aggiornamento delle impostazioni di posizione");
    }
  };

  const handleEnableLocation = (checked: boolean) => {
    if (!currentUser) {
      // For non-authenticated users, use the guest location store
      updateGuestLocationSetting(checked);
      toast.success(
        `Posizione ${checked ? "abilitata" : "disabilitata"} con successo`
      );
    } else {
      // For authenticated users, implement user-specific location setting
      // This can be implemented later if needed
      toast.info("Funzionalità disponibile per utenti non autenticati");
    }
  };

  const handleLogout = async () => {
    const { error } = await supabase.auth.signOut();
    if (error) {
      toast.error(error.message);
    } else {
      // Clear all React Query cache to ensure no stale user data remains
      queryClient.clear();
      //   toast.success("Logout effettuato con successo");
      navigate("/");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {currentUser ? (
        // Existing authenticated user profile content

        <UnifiedHeader
          variant="default"
          title="Profilo"
          showShadow={true}
          showNotifications={true}
          isBusiness={isBusinessMode}
        />
      ) : (
        // Non-authenticated user sees the rewards card
        <RewardsCard />
      )}

      <main className="pt-16 pb-20 px-4">
        {/* Group invitations section - only for authenticated users */}
        {/* {user && <GroupInvitesSection />} */}

        {currentUser ? (
          <section className="bg-white rounded-xl p-4 shadow-sm">
            <div className="flex items-center space-x-4">
              <div className="w-20 h-20 rounded-full bg-brand-light flex items-center justify-center">
                <span className="text-2xl font-semibold text-brand-primary">
                  {currentUser?.email?.[0]?.toUpperCase() || "U"}
                </span>
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-semibold text-gray-800">
                  {currentUser?.email?.split("@")[0] || "Utente"}
                </h2>
                <p className="text-gray-500 text-sm">{currentUser?.email}</p>
              </div>
            </div>
            <button
              onClick={() => navigate("/edit-profile")}
              className="mt-4 w-full py-2 border border-brand-primary text-brand-primary rounded-lg font-medium hover:bg-brand-primary hover:text-white transition-colors"
            >
              Modifica Profilo
            </button>

            <div className="mt-4 space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Modalità Demo Mappa
                </span>
                <div className="flex items-center space-x-2">
                  <Switch
                    checked={mapDemo}
                    onCheckedChange={handleMapDemoToggle}
                    disabled={isLoading}
                    className="h-6 w-12 transition-all duration-200 data-[state=checked]:bg-brand-primary data-[state=unchecked]:bg-gray-300 hover:data-[state=unchecked]:bg-gray-400"
                  />
                  <button
                    onClick={refreshSettings}
                    className="p-1 text-gray-500 hover:text-brand-primary"
                    disabled={isLoading}
                    title="Aggiorna impostazioni"
                  >
                    <RefreshCw
                      size={16}
                      className={`${isLoading ? "animate-spin" : ""}`}
                    />
                  </button>
                </div>
              </div>
              {/* Demo Coordinates Editor - shown only when demo mode is enabled */}
              {mapDemo && (
                <div className="mt-4">
                  {/* Debug info */}
                  <div className="mb-2 p-2 bg-gray-100 rounded text-xs">
                    <div>
                      <strong>Debug Info:</strong>
                    </div>
                    <div>User ID: {currentUser?.id || "null"}</div>
                    <div>UserDetails loaded: {userDetails ? "Yes" : "No"}</div>
                    <div>
                      Demo coords from DB:{" "}
                      {demoCoords
                        ? `${demoCoords.lat}, ${demoCoords.lng}`
                        : "null"}
                    </div>
                    <div>
                      Raw demo_location:{" "}
                      {JSON.stringify(userDetails?.demo_location)}
                    </div>
                  </div>

                  <DemoCoordinatesEditor
                    demoLatitude={demoCoords?.lat || null}
                    demoLongitude={demoCoords?.lng || null}
                    onUpdate={updateDemoCoordinates}
                  />
                </div>
              )}
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Tema Arancione
                </span>
                <Switch
                  checked={variants.colorPalette === "alternative"}
                  onCheckedChange={(checked) =>
                    setColorPalette(checked ? "alternative" : "default")
                  }
                  className="h-6 w-12 transition-all duration-200 data-[state=checked]:bg-brand-primary data-[state=unchecked]:bg-gray-300 hover:data-[state=unchecked]:bg-gray-400"
                />
              </div>
            </div>
          </section>
        ) : null}

        <section className="bg-white rounded-xl shadow-sm divide-y">
          <div className="mt-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">
                <MapPin className="h-4 w-4 mr-2" />
                Abilita posizione
              </span>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={currentUser ? false : isLocationEnabledForGuests}
                  onCheckedChange={handleEnableLocation}
                  disabled={isLoading}
                  className="h-6 w-12 transition-all duration-200 data-[state=checked]:bg-brand-primary data-[state=unchecked]:bg-gray-300 hover:data-[state=unchecked]:bg-gray-400"
                />
              </div>
            </div>
          </div>
          {menuItems
            .filter((item) => !item.authenticationRequired || currentUser)
            .filter((item) => !item.isAdmin || currentUser.isAdmin)
            .filter(
              (item) => item.id !== "subscription" || hasActiveSubscription
            )
            .map((item, index) => (
              <motion.button
                key={index}
                whileTap={{ scale: 0.98 }}
                className="w-full px-4 py-3 flex items-center justify-between"
                onClick={() => {
                  console.log(
                    "Profile menu item clicked:",
                    item.label,
                    "path:",
                    item.path
                  );
                  if (item.action) {
                    item.action();
                  } else if (item.path) {
                    console.log("Navigating to:", item.path);
                    navigate(item.path);
                  }
                }}
              >
                <div className="flex items-center space-x-3">
                  <item.icon className="h-5 w-5 text-gray-500" />
                  <span className="text-gray-700">{item.label}</span>
                </div>
                {"count" in item && item.count && (
                  <span className="bg-brand-light text-brand-primary px-2 py-1 rounded-full text-sm">
                    {String(item.count)}
                  </span>
                )}
              </motion.button>
            ))}
        </section>

        {currentUser && (
          <button
            onClick={handleLogout}
            className="w-full mt-6 px-4 py-3 bg-white rounded-xl shadow-sm flex items-center justify-between text-red-500 font-medium"
          >
            <div className="flex items-center space-x-3">
              <LogOut className="h-5 w-5" />
              <span>Esci</span>
            </div>
          </button>
        )}
      </main>
      {currentUser ? (
        <div className="fixed bottom-12 left-0 right-0 flex justify-center z-40">
          <motion.button
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            whileHover={{ scale: 1.1 }}
            onClick={() => {
              if (isBusinessMode) {
                // If already in business mode, allow toggle back to user mode
                toggleBusinessMode();
              } else {
                // If trying to switch to business mode, check subscription
                if (hasActiveSubscription) {
                  toggleBusinessMode();
                } else {
                  // No active subscription, redirect to subscription page
                  navigate("/subscription");
                }
              }
            }}
            className={`p-4 rounded-full shadow-lg flex items-center gap-2 ${
              isBusinessMode
                ? "bg-brand-primary text-white"
                : "bg-white text-brand-primary"
            }`}
          >
            {isBusinessMode ? (
              <User className="h-5 w-5" />
            ) : (
              <Store className="h-5 w-5" />
            )}
            <span className="text-sm font-medium">
              {isBusinessMode ? "Passa ad Utente" : "Passa a Business"}
            </span>
          </motion.button>
        </div>
      ) : null}

      <BottomNavigationBar isBusiness={isBusinessMode} />

      {/* Fixed position app version */}
      {/* {appVersion && (
        <div className="fixed bottom-16 left-0 right-0 text-center text-xs text-gray-500 py-1 bg-gray-50/80 backdrop-blur-sm z-30">
          Ver. {appVersion}
        </div>
      )} */}

      {/* Modals */}
      {isTermsOpen ? (
        <TermsModal
          isOpen={isTermsOpen}
          onClose={() => setIsTermsOpen(false)}
        />
      ) : null}
      {isPrivacyOpen ? (
        <PrivacyModal
          isOpen={isPrivacyOpen}
          onClose={() => setIsPrivacyOpen(false)}
        />
      ) : null}
    </div>
  );
};

export default Profile;
